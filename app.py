from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime
import pandas as pd

# 简化导入，先测试基本功能
try:
    from src.core.patient_processor import PatientProcessor
    from src.core.performance_calculator import PerformanceCalculator
    from src.core.report_generator import ReportGenerator
    from src.core.doctor_manager import DoctorManager
    from src.utils.logger import setup_logger
    logger = setup_logger()
except ImportError as e:
    print(f"Warning: Could not import some modules: {e}")
    print("Running in basic mode...")
    logger = None

app = Flask(__name__)
app.config['SECRET_KEY'] = 'local-development-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///performance_dev.db'  # 本地开发用SQLite
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('output', exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录访问此页面'

# 数据库模型
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='doctor')  # doctor, admin
    doctor_name = db.Column(db.String(50))  # 关联的医生姓名
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # 关联的计算任务
    tasks = db.relationship('CalculationTask', backref='user', lazy=True)

class CalculationTask(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    task_name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    result_file = db.Column(db.String(255))  # 结果文件路径
    error_message = db.Column(db.Text)
    
    # 任务参数
    data_folder = db.Column(db.String(255))  # 数据文件夹路径
    duplicate_strategy = db.Column(db.String(20), default='keep_first')
    
class DoctorGroup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    group_name = db.Column(db.String(50), nullable=False)
    doctors = db.Column(db.Text)  # JSON格式存储医生列表
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 路由定义
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        doctor_name = request.form.get('doctor_name')
        
        # 验证用户是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return render_template('register.html')
            
        if User.query.filter_by(email=email).first():
            flash('邮箱已被注册', 'error')
            return render_template('register.html')
            
        # 创建新用户
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            doctor_name=doctor_name
        )
        
        db.session.add(user)
        db.session.commit()
        
        flash('注册成功，请登录', 'success')
        return redirect(url_for('login'))
        
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password) and user.is_active:
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')
            
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已成功退出登录', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # 获取用户的计算任务
    tasks = CalculationTask.query.filter_by(user_id=current_user.id).order_by(CalculationTask.created_at.desc()).limit(10).all()
    return render_template('dashboard.html', tasks=tasks, current_time=datetime.now())

@app.route('/calculate', methods=['GET', 'POST'])
@login_required
def calculate():
    if request.method == 'POST':
        try:
            # 处理文件上传
            uploaded_files = request.files.getlist('files')
            task_name = request.form.get('task_name', f'任务_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            duplicate_strategy = request.form.get('duplicate_strategy', 'keep_first')
            
            if not uploaded_files or len(uploaded_files) < 5:
                flash('请上传所有必需的文件（patients.xlsx, 1.csv, 2.csv, 3.csv, icu.csv）', 'error')
                return render_template('calculate.html')
            
            # 创建用户专属文件夹
            user_folder = os.path.join(app.config['UPLOAD_FOLDER'], str(current_user.id), datetime.now().strftime('%Y%m%d_%H%M%S'))
            os.makedirs(user_folder, exist_ok=True)
            
            # 保存上传的文件
            file_paths = {}
            required_files = ['patients.xlsx', '1.csv', '2.csv', '3.csv', 'icu.csv']
            
            for file in uploaded_files:
                if file.filename in required_files:
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(user_folder, filename)
                    file.save(filepath)
                    file_paths[filename] = filepath
            
            # 检查所有必需文件是否都已上传
            missing_files = [f for f in required_files if f not in file_paths]
            if missing_files:
                flash(f'缺少文件: {", ".join(missing_files)}', 'error')
                return render_template('calculate.html')
            
            # 创建计算任务
            task = CalculationTask(
                user_id=current_user.id,
                task_name=task_name,
                status='processing',
                data_folder=user_folder,
                duplicate_strategy=duplicate_strategy
            )
            db.session.add(task)
            db.session.commit()
            
            # 执行计算（这里应该使用异步任务队列，如Celery）
            print(f"🔄 开始执行计算任务 {task.id}")
            print(f"📁 用户文件夹: {user_folder}")
            print(f"🔧 去重策略: {duplicate_strategy}")
            
            # 检查上传的文件
            print("📋 检查上传的文件:")
            for filename in ['patients.xlsx', '1.csv', '2.csv', '3.csv', 'icu.csv']:
                filepath = os.path.join(user_folder, filename)
                if os.path.exists(filepath):
                    size = os.path.getsize(filepath)
                    print(f"   ✅ {filename}: {size} 字节")
                else:
                    print(f"   ❌ {filename}: 文件不存在")
            
            result = perform_calculation(task.id, user_folder, duplicate_strategy)
            
            print(f"🎯 计算结果: success={result['success']}")
            if not result['success']:
                print(f"❌ 错误信息: {result['error']}")
            
            if result['success']:
                task.status = 'completed'
                task.completed_at = datetime.utcnow()
                task.result_file = result['output_file']
                flash('绩效计算完成！', 'success')
                print(f"✅ 任务完成，输出文件: {result['output_file']}")
            else:
                task.status = 'failed'
                task.error_message = result['error']
                flash(f'计算失败: {result["error"]}', 'error')
                print(f"❌ 任务失败: {result['error']}")
            
            db.session.commit()
            return redirect(url_for('dashboard'))
            
        except Exception as e:
            logger.error(f"计算过程出错: {str(e)}")
            flash('计算过程出现错误，请稍后重试', 'error')
            
    return render_template('calculate.html', current_time=datetime.now())

@app.route('/doctor-management')
@login_required
def doctor_management():
    # 加载医生分组配置
    doctor_manager = DoctorManager()
    groups = doctor_manager.get_all_groups()
    return render_template('doctor_management.html', groups=groups)

@app.route('/api/doctor-groups')
@login_required
def get_doctor_groups():
    doctor_manager = DoctorManager()
    return jsonify(doctor_manager.get_all_groups())

@app.route('/api/doctor-groups', methods=['POST'])
@login_required
def update_doctor_groups():
    try:
        data = request.get_json()
        doctor_manager = DoctorManager()
        doctor_manager.save_groups(data)
        return jsonify({'success': True, 'message': '医生分组更新成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download/<int:task_id>')
@login_required
def download_result(task_id):
    task = CalculationTask.query.get_or_404(task_id)

    # 确保用户只能下载自己的任务结果
    if task.user_id != current_user.id:
        flash('无权限访问此文件', 'error')
        return redirect(url_for('dashboard'))

    if task.status != 'completed' or not task.result_file:
        flash('文件不存在或任务未完成', 'error')
        return redirect(url_for('dashboard'))

    return send_file(task.result_file, as_attachment=True)

@app.route('/api/task/<int:task_id>', methods=['DELETE'])
@login_required
def delete_task(task_id):
    """删除计算任务"""
    try:
        task = CalculationTask.query.get_or_404(task_id)

        # 确保用户只能删除自己的任务
        if task.user_id != current_user.id:
            return jsonify({'success': False, 'error': '无权限删除此任务'})

        # 删除相关文件
        try:
            # 删除数据文件夹
            if task.data_folder and os.path.exists(task.data_folder):
                import shutil
                shutil.rmtree(task.data_folder)
                logger.info(f"删除数据文件夹: {task.data_folder}")

            # 删除结果文件
            if task.result_file and os.path.exists(task.result_file):
                os.remove(task.result_file)
                logger.info(f"删除结果文件: {task.result_file}")

        except Exception as e:
            logger.warning(f"删除任务文件时出错: {str(e)}")
            # 文件删除失败不影响数据库记录删除

        # 删除数据库记录
        db.session.delete(task)
        db.session.commit()

        logger.info(f"用户 {current_user.username} 删除了任务 {task_id}")
        return jsonify({'success': True, 'message': '任务删除成功'})

    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

@app.route('/api/task/<int:task_id>/progress')
@login_required
def get_task_progress(task_id):
    """获取任务进度"""
    try:
        task = CalculationTask.query.get_or_404(task_id)

        # 确保用户只能查看自己的任务
        if task.user_id != current_user.id:
            return jsonify({'error': '无权限访问此任务'})

        # 根据任务状态返回进度信息
        if task.status == 'pending':
            progress = 0
            current_step = '等待开始'
        elif task.status == 'processing':
            progress = 50
            current_step = '正在计算中...'
        elif task.status == 'completed':
            progress = 100
            current_step = '计算完成'
        elif task.status == 'failed':
            progress = 0
            current_step = '计算失败'
        else:
            progress = 0
            current_step = '未知状态'

        return jsonify({
            'progress': progress,
            'status': task.status,
            'current_step': current_step,
            'start_time': task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else '',
            'end_time': task.completed_at.strftime('%Y-%m-%d %H:%M:%S') if task.completed_at else '',
            'error_message': task.error_message
        })

    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}")
        return jsonify({'error': f'获取进度失败: {str(e)}'})

def perform_calculation(task_id, data_folder, duplicate_strategy):
    """执行绩效计算"""
    try:
        # 使用简化的计算器
        from simple_calculator import SimplePerformanceCalculator
        calculator = SimplePerformanceCalculator()
        
        print(f"🔄 开始执行绩效计算任务 {task_id}")
        print(f"📁 数据文件夹: {data_folder}")
        print(f"🔧 去重策略: {duplicate_strategy}")
        
        # 详细检查数据文件夹和文件
        print(f"📋 详细检查数据文件夹: {data_folder}")
        if not os.path.exists(data_folder):
            error_msg = f"数据文件夹不存在: {data_folder}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}
        
        # 检查所有必需文件
        required_files = ['patients.xlsx', '1.csv', '2.csv', '3.csv', 'icu.csv']
        missing_files = []
        file_info = {}
        
        for filename in required_files:
            filepath = os.path.join(data_folder, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                file_info[filename] = {'path': filepath, 'size': size, 'exists': True}
                print(f"   ✅ {filename}: {size} 字节")
            else:
                file_info[filename] = {'path': filepath, 'size': 0, 'exists': False}
                missing_files.append(filename)
                print(f"   ❌ {filename}: 文件不存在")
        
        if missing_files:
            error_msg = f"缺少必需文件: {', '.join(missing_files)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}
        
        # 1. 加载患者数据
        patients_file = file_info['patients.xlsx']['path']
        print(f"📊 加载患者数据: {patients_file}")
        if not calculator.load_patient_data(patients_file):
            error_msg = '患者数据加载失败 - 可能是文件格式错误或文件损坏'
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}
        
        # 2. 去除重复数据
        print(f"🔄 执行数据去重: {duplicate_strategy}")
        if not calculator.remove_duplicates(duplicate_strategy):
            error_msg = '数据去重处理失败'
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}
        
        # 3. 加载绩效数据
        csv_files = {
            '1.csv': file_info['1.csv']['path'],
            '2.csv': file_info['2.csv']['path'],
            '3.csv': file_info['3.csv']['path'],
            'icu.csv': file_info['icu.csv']['path']
        }
        
        print(f"📊 开始加载绩效数据...")
        print(f"   将要加载的文件: {list(csv_files.keys())}")
        
        # 🔍 深度调试：检查每个CSV文件的实际内容
        print(f"🔍 深度调试 - 检查上传的CSV文件内容:")
        for file_key, file_path in csv_files.items():
            try:
                print(f"   📄 检查 {file_key}:")
                print(f"      路径: {file_path}")
                
                # 读取原始内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    raw_content = f.read()
                    lines = raw_content.split('\n')
                    print(f"      原始行数: {len(lines)}")
                    print(f"      前3行内容: {lines[:3]}")
                
                # 尝试用pandas读取
                import pandas as pd
                df = pd.read_csv(file_path, encoding='utf-8')
                print(f"      pandas读取成功:")
                print(f"        数据行数: {len(df)}")
                print(f"        列名: {list(df.columns)}")
                print(f"        列名类型: {[type(col).__name__ for col in df.columns]}")
                
                # 检查必需列
                required_cols = ['xm', 'xz', 'zx']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    print(f"        ❌ 缺少必需列: {missing_cols}")
                else:
                    print(f"        ✅ 包含所有必需列")
                    
                # 显示第一行数据
                if len(df) > 0:
                    print(f"        第一行数据: {df.iloc[0].to_dict()}")
                    
            except Exception as e:
                print(f"      ❌ 文件检查失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 🔍 与测试文件对比
        print(f"🔍 与测试文件对比:")
        test_files = {
            '1.csv': 'test_data/1.csv',
            '2.csv': 'test_data/2.csv', 
            '3.csv': 'test_data/3.csv',
            'icu.csv': 'test_data/icu.csv'
        }
        
        for file_key in csv_files.keys():
            if file_key in test_files:
                uploaded_path = csv_files[file_key]
                test_path = test_files[file_key]
                
                try:
                    # 比较文件大小
                    uploaded_size = os.path.getsize(uploaded_path)
                    test_size = os.path.getsize(test_path) if os.path.exists(test_path) else 0
                    
                    print(f"   📊 {file_key}:")
                    print(f"      上传文件大小: {uploaded_size} 字节")
                    print(f"      测试文件大小: {test_size} 字节")
                    
                    if uploaded_size != test_size:
                        print(f"      ⚠️ 文件大小不匹配!")
                        
                        # 比较内容
                        if os.path.exists(test_path):
                            with open(uploaded_path, 'r', encoding='utf-8') as f1, \
                                 open(test_path, 'r', encoding='utf-8') as f2:
                                uploaded_content = f1.read()
                                test_content = f2.read()
                                
                                print(f"      上传文件内容: {repr(uploaded_content[:100])}")
                                print(f"      测试文件内容: {repr(test_content[:100])}")
                    else:
                        print(f"      ✅ 文件大小匹配")
                        
                except Exception as e:
                    print(f"      ❌ 比较失败: {e}")
        
        if not calculator.load_performance_data(csv_files):
            error_msg = '绩效数据加载失败 - 请查看上方详细调试信息'
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}
        
        # 4. 计算绩效
        results = calculator.calculate_performance()
        if results is None:
            return {
                'success': False,
                'error': '绩效计算失败'
            }
        
        # 5. 生成Excel报告
        output_filename = f'绩效统计报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        output_path = os.path.join('output', output_filename)
        
        if not calculator.generate_excel_report(results, output_path):
            return {
                'success': False,
                'error': 'Excel报告生成失败'
            }
        
        print(f"✅ 绩效计算任务 {task_id} 完成！")
        return {
            'success': True,
            'output_file': output_path,
            'message': '计算完成',
            'stats': {
                'total_doctors': results['summary']['total_doctors'],
                'total_groups': results['summary']['total_groups'],
                'total_patients': results['summary']['total_patients'],
                'total_records': results['summary']['total_records']
            }
        }
        
    except Exception as e:
        error_msg = f"计算失败: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # 创建默认管理员用户
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                doctor_name='系统管理员'
            )
            db.session.add(admin)
            db.session.commit()
            print("✅ 创建默认管理员用户: admin / admin123")
    
    # 获取端口设置
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--port', type=int, default=5000, help='端口号')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址')
    args = parser.parse_args()
    
    print(f"🚀 启动Web服务器...")
    print(f"📱 访问地址: http://{args.host}:{args.port}")
    print(f"🔑 管理员账号: admin / admin123")
    print(f"❌ 按 Ctrl+C 停止服务器")
    
    try:
        app.run(debug=True, host=args.host, port=args.port)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")